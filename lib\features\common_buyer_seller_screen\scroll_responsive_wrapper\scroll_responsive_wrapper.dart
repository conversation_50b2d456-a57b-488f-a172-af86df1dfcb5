import 'package:flutter/material.dart';
import 'package:swadesic/util/app_constants.dart';

/// A wrapper widget that provides scroll-responsive behavior for app bar and bottom navigation
/// When user scrolls up, both app bar and bottom navigation hide with smooth animations
/// When user scrolls down, both elements show again
class ScrollResponsiveWrapper extends StatefulWidget {
  final Widget child;
  final ScrollController? scrollController;
  final AppBar? appBar;
  final bool enableScrollResponsive;
  
  const ScrollResponsiveWrapper({
    Key? key,
    required this.child,
    this.scrollController,
    this.appBar,
    this.enableScrollResponsive = true,
  }) : super(key: key);

  @override
  State<ScrollResponsiveWrapper> createState() => _ScrollResponsiveWrapperState();
}

class _ScrollResponsiveWrapperState extends State<ScrollResponsiveWrapper>
    with TickerProviderStateMixin {
  
  // Animation controllers for smooth transitions
  late AnimationController _appBarAnimationController;
  late AnimationController _bottomNavAnimationController;
  
  // Animations for slide transitions
  late Animation<Offset> _appBarSlideAnimation;
  late Animation<Offset> _bottomNavSlideAnimation;
  
  // Scroll tracking variables
  double _previousScrollOffset = 0.0;
  bool _isAppBarVisible = true;
  bool _isBottomNavVisible = true;

  // Scroll threshold and timing to prevent jittery behavior
  static const double _scrollThreshold = 5.0;
  static const int _scrollDebounceMs = 100;
  DateTime? _lastScrollTime;

  // Track scroll direction over multiple events for stability
  List<bool> _recentScrollDirections = [];
  static const int _directionHistorySize = 3;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupScrollListener();
  }
  
  void _initializeAnimations() {
    // Initialize animation controllers
    _appBarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _bottomNavAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // Initialize slide animations
    _appBarSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -1.0), // Slide up to hide
    ).animate(CurvedAnimation(
      parent: _appBarAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _bottomNavSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, 1.0), // Slide down to hide
    ).animate(CurvedAnimation(
      parent: _bottomNavAnimationController,
      curve: Curves.easeInOut,
    ));
    
    // Start with both elements visible
    _appBarAnimationController.reset();
    _bottomNavAnimationController.reset();
  }
  
  void _setupScrollListener() {
    if (widget.scrollController != null && widget.enableScrollResponsive) {
      widget.scrollController!.addListener(_handleScroll);
    }
  }
  
  void _handleScroll() {
    if (!widget.enableScrollResponsive) return;

    final now = DateTime.now();
    final currentScrollOffset = widget.scrollController!.offset;
    final scrollDelta = currentScrollOffset - _previousScrollOffset;

    // Debounce scroll events to prevent excessive processing
    if (_lastScrollTime != null &&
        now.difference(_lastScrollTime!).inMilliseconds < _scrollDebounceMs) {
      return;
    }
    _lastScrollTime = now;

    // Only react to significant scroll movements to prevent jittery behavior
    if (scrollDelta.abs() < _scrollThreshold) return;

    // Determine scroll direction and add to history for stability
    final isScrollingUp = scrollDelta > 0;
    _recentScrollDirections.add(isScrollingUp);

    // Keep only recent directions for analysis
    if (_recentScrollDirections.length > _directionHistorySize) {
      _recentScrollDirections.removeAt(0);
    }

    // Only change visibility if we have consistent direction over multiple events
    if (_recentScrollDirections.length >= _directionHistorySize) {
      final upCount = _recentScrollDirections.where((isUp) => isUp).length;
      final consistentScrollUp = upCount >= (_directionHistorySize * 0.7).ceil();
      final consistentScrollDown = upCount <= (_directionHistorySize * 0.3).floor();

      // Handle app bar and bottom navigation visibility based on consistent direction
      if (consistentScrollUp && _isAppBarVisible) {
        // Hide both app bar and bottom navigation when consistently scrolling up
        _hideAppBarAndBottomNav();
      } else if (consistentScrollDown && !_isAppBarVisible) {
        // Show both app bar and bottom navigation when consistently scrolling down
        _showAppBarAndBottomNav();
      }
    }

    _previousScrollOffset = currentScrollOffset;
  }
  
  void _hideAppBarAndBottomNav() {
    if (!_isAppBarVisible) return;
    
    setState(() {
      _isAppBarVisible = false;
      _isBottomNavVisible = false;
    });
    
    // Animate hiding
    _appBarAnimationController.forward();
    _bottomNavAnimationController.forward();
    
    // Notify bottom navigation to hide
    _notifyBottomNavigationVisibility(false);
  }
  
  void _showAppBarAndBottomNav() {
    if (_isAppBarVisible) return;
    
    setState(() {
      _isAppBarVisible = true;
      _isBottomNavVisible = true;
    });
    
    // Animate showing
    _appBarAnimationController.reverse();
    _bottomNavAnimationController.reverse();
    
    // Notify bottom navigation to show
    _notifyBottomNavigationVisibility(true);
  }
  
  void _notifyBottomNavigationVisibility(bool isVisible) {
    // Use the existing bottom navigation refresh controller to trigger updates
    AppConstants.bottomNavigationVisibilityCtrl.sink.add(isVisible);
  }
  
  @override
  void dispose() {
    if (widget.scrollController != null) {
      widget.scrollController!.removeListener(_handleScroll);
    }
    _appBarAnimationController.dispose();
    _bottomNavAnimationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Animated app bar
      appBar: widget.appBar != null
          ? PreferredSize(
              preferredSize: const Size.fromHeight(kToolbarHeight),
              child: SlideTransition(
                position: _appBarSlideAnimation,
                child: widget.appBar!,
              ),
            )
          : null,
      body: widget.child,
    );
  }
}

/// Extension to add scroll responsive behavior to existing widgets
extension ScrollResponsiveExtension on Widget {
  Widget wrapWithScrollResponsive({
    ScrollController? scrollController,
    AppBar? appBar,
    bool enableScrollResponsive = true,
  }) {
    return ScrollResponsiveWrapper(
      scrollController: scrollController,
      appBar: appBar,
      enableScrollResponsive: enableScrollResponsive,
      child: this,
    );
  }
}
