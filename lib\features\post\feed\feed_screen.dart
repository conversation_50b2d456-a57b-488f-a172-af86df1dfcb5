import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/banner/banner.dart';
import 'package:swadesic/features/common_buyer_seller_screen/adding_post_progress/adding_post_progress.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/all_feed/all_feed_screen.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/features/post/feed/feed_pagination.dart';
import 'package:swadesic/features/post/feed/store_creation_announcement.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

//region Feed screen
class FeedScreen extends StatefulWidget {
  final String userOrStoreFeedReference;
  final ScrollController? externalScrollController;

  const FeedScreen({
    super.key,
    required this.userOrStoreFeedReference,
    this.externalScrollController,
  });

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}
//endregion

class _FeedScreenState extends State<FeedScreen>
    with AutomaticKeepAliveClientMixin<FeedScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //Feed bloc
  late FeedBloc feedBloc;

  //region Init
  @override
  void initState() {
    feedBloc = FeedBloc(context, widget.userOrStoreFeedReference, widget.externalScrollController);
    feedBloc.init();
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    feedBloc.dispose();
    super.dispose();
  }

  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: RefreshIndicator(
        color: AppColors.brandBlack,
        onRefresh: () async {
          // Show loading state and wait for refresh to complete
          feedBloc.feedStateCtrl.sink.add(FeedState.Loading);
          await feedBloc.init();

          // If we're showing all feed posts, refresh them
          if (feedBloc.currentApiCallStatus == FeedState.Empty) {
            // Find the AllFeedScreen widget and get its bloc
            final allFeedWidget =
                context.findAncestorWidgetOfExactType<AllFeedScreen>();
            if (allFeedWidget != null) {
              // Refresh the all feed screen
              Navigator.of(context)
                  .push(
                      MaterialPageRoute(builder: (context) => AllFeedScreen()))
                  .then((_) => Navigator.of(context).pop());
            }
          }
        },
        child: CustomScrollView(
          controller: feedBloc.scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverToBoxAdapter(child: banner()),
            SliverToBoxAdapter(child: appUpdate()),
            SliverToBoxAdapter(child: updateEmail()),
            SliverToBoxAdapter(child: storeCreationAnnouncement()),
            StreamBuilder<FeedState>(
              stream: feedBloc.feedStateCtrl.stream,
              initialData: FeedState.Loading,
              builder: (context, feedSnapshot) {
                if (feedSnapshot.data == FeedState.Loading &&
                    feedBloc.feedList.isEmpty) {
                  return SliverToBoxAdapter(
                    child: Container(
                      alignment: Alignment.center,
                      height: MediaQuery.of(context).size.height * 0.5,
                      child: AppCommonWidgets.appCircularProgress(),
                    ),
                  );
                }
                if (feedSnapshot.data == FeedState.Empty) {
                  return SliverToBoxAdapter(
                      child: AllFeedScreen(
                          parentScrollController: feedBloc.scrollController));
                }
                return feedList();
              },
            ),
            StreamBuilder<FeedPaginationState>(
                stream: feedBloc.feedPagination.feedPaginationStateCtrl.stream,
                initialData: FeedPaginationState.Loading,
                builder: (context, snapshot) {
                  if (snapshot.data == FeedPaginationState.Empty &&
                      !feedBloc.hasMoreData &&
                      feedBloc.feedList.isNotEmpty) {
                    return SliverToBoxAdapter(
                        child: AllFeedScreen(
                            parentScrollController: feedBloc.scrollController));
                  }
                  if (snapshot.data == FeedPaginationState.Loading &&
                      feedBloc.isPaginationLoading) {
                    return SliverToBoxAdapter(
                        child: VisibilityDetector(
                            key: UniqueKey(),
                            onVisibilityChanged: (visibilityInfo) {
                              var visiblePercentage =
                                  visibilityInfo.visibleFraction * 100;
                              if (visiblePercentage == 100) {
                                feedBloc.feedPagination.getPaginationFeeds();
                              }
                            },
                            child: AppCommonWidgets.appCircularProgress(
                                isPaginationProgress: true)));
                  }
                  return const SliverToBoxAdapter(child: SizedBox.shrink());
                }),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Body
  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        // Show loading state and wait for refresh to complete
        feedBloc.feedStateCtrl.sink.add(FeedState.Loading);
        await feedBloc.init();
      },
      child: CustomScrollView(
        controller: feedBloc.scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverToBoxAdapter(child: banner()),
          SliverToBoxAdapter(child: appUpdate()),
          SliverToBoxAdapter(child: updateEmail()),
          SliverToBoxAdapter(
            child: StreamBuilder<FeedState>(
              stream: feedBloc.feedStateCtrl.stream,
              initialData: FeedState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == FeedState.Loading &&
                    feedBloc.feedList.isEmpty) {
                  return Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.5,
                    child: AppCommonWidgets.appCircularProgress(),
                  );
                }
                if (snapshot.data == FeedState.Empty) {
                  return AllFeedScreen(
                      parentScrollController: feedBloc.scrollController);
                }
                return feedList();
              },
            ),
          ),
        ],
      ),
    );
  }

//endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.postTitle,
        isCartVisible: false,
        isMembershipVisible: false,
        isDefaultMenuVisible: false);
  }

  //endregion

  //region Banner
  Widget banner() {
    return StreamBuilder<bool>(
        stream: feedBloc.appUpdateCheckCtrl.stream,
        builder: (context, snapshot) {
          return const BannerScreen(isFromFeed: true);
        });
  }

  //endregion

  //region App update
  Widget appUpdate() {
    return StreamBuilder<bool>(
        stream: feedBloc.appUpdateCheckCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          if (snapshot.data!) {
            return Container(
              margin: const EdgeInsets.all(10),
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              decoration: BoxDecoration(
                color: AppColors.lightGreen2.withOpacity(0.6),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Flexible(
                            child: Text(
                          AppStrings.newVersionApp,
                          maxLines: 2,
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.writingBlack0),
                        )),
                        const SizedBox(height: 5),
                        Row(
                          children: [
                            Text(
                              AppStrings.highPriority,
                              style: AppTextStyle.smallTextRegular(
                                  textColor: AppColors.red, isUnderline: true),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      ///Check app update
                      CommonMethods.appUpdateCheck();
                    },
                    child: Column(
                      children: [
                        SvgPicture.asset(
                          AppImages.install,
                          height: 25,
                          width: 25,
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        Text(
                          AppStrings.tapToInstall,
                          style: AppTextStyle.smallText(
                              textColor: AppColors.brandBlack),
                        )
                      ],
                    ),
                  )
                ],
              ),
            );
          }
          return const SizedBox();
        });
  }

  //endregion

  //region Update email
  Widget updateEmail() {
    return Consumer<LoggedInUserInfoDataModel>(
      builder: (BuildContext context, value, Widget? child) {
        //If has data and not a static user
        if (value.userDetail != null &&
            value.userDetail!.userReference != null &&
            value.userDetail!.email == null &&
            !CommonMethods().isStaticUser()) {
          return Container(
            margin: const EdgeInsets.all(10),
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            decoration: BoxDecoration(
              color: AppColors.lightGreen2.withOpacity(0.6),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                          child: Text(
                        AppStrings.completeEmailVerification,
                        maxLines: 2,
                        style: AppTextStyle.contentHeading0(
                            textColor: AppColors.writingBlack0),
                      )),
                      const SizedBox(height: 5),
                      Row(
                        children: [
                          Text(
                            AppStrings.highPriority,
                            style: AppTextStyle.smallTextRegular(
                                textColor: AppColors.red, isUnderline: true),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    feedBloc.goToEmailVerify();
                  },
                  child: Column(
                    children: [
                      SvgPicture.asset(
                        AppImages.mail,
                        height: 25,
                        width: 25,
                      ),
                      const SizedBox(
                        height: 3,
                      ),
                      Text(
                        AppStrings.verify,
                        style: AppTextStyle.smallText(
                            textColor: AppColors.brandBlack),
                      )
                    ],
                  ),
                )
              ],
            ),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }
  //endregion

  //region Store Creation Announcement
  Widget storeCreationAnnouncement() {
    return StreamBuilder<bool>(
      stream: feedBloc.storeCreationAnnouncementCtrl.stream,
      initialData: false,
      builder: (context, snapshot) {
        if (snapshot.data == true) {
          return const StoreCreationAnnouncement();
        } else {
          return const SizedBox();
          // return const StoreCreationAnnouncement();
        }
      },
    );
  }
  //endregion

//region Feed list
  Widget feedList() {
    if (feedBloc.currentApiCallStatus == FeedState.Empty) {
      return SliverToBoxAdapter(
          child:
              AllFeedScreen(parentScrollController: feedBloc.scrollController));
    }

    return Consumer2<PostDataModel, ProductDataModel>(
      builder: (context, postDataModel, productDataModel, child) {
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              if (index == feedBloc.feedList.length) {
                return Visibility(
                  visible: feedBloc.isPaginationLoading,
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    child: Center(
                        child: AppCommonWidgets.appCircularProgress(
                            isPaginationProgress: true)),
                  ),
                );
              }

              // Safety check for index
              if (index >= feedBloc.feedList.length) {
                return const SizedBox();
              }

              final item = feedBloc.feedList[index];
              if (item == null) {
                return const SizedBox();
              }

              if (item is PostDetail) {
                // Safety check for postDataModel
                if (postDataModel.allPostDetailList.isEmpty) {
                  return const SizedBox();
                }

                final postDetail = postDataModel.allPostDetailList.firstWhere(
                  (element) =>
                      element.postOrCommentReference != null &&
                      item.postOrCommentReference != null &&
                      element.postOrCommentReference ==
                          item.postOrCommentReference,
                  orElse: () => PostDetail(),
                );

                // Safety check for postDetail
                if (postDetail.postOrCommentReference == null) {
                  return const SizedBox();
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: RepaintBoundary(
                    child: PostCard(
                      key:
                          ValueKey('post_${postDetail.postOrCommentReference}'),
                      postDetail: postDetail,
                      isCustomTitleVisible: true,
                      customTitle: item.contentHeaderText,
                      onTapDelete: () =>
                          feedBloc.confirmDelete(postDetail: postDetail),
                      onTapDrawer: () =>
                          feedBloc.onTapDrawer(postDetail: postDetail),
                      onTapEdit: () =>
                          feedBloc.goToEditPost(postDetail: postDetail),
                      onTapHeart: () =>
                          feedBloc.onTapHeart(postDetail: postDetail),
                      onTapShare: () =>
                          feedBloc.onTapShare(postDetail: postDetail),
                      onTapProfileImage: () => feedBloc.onTapUserOrStoreIcon(
                          reference:
                              postDetail.createdBy!.userOrStoreReference!),
                      onTapPost: () =>
                          feedBloc.goToSinglePostView(postDetail: postDetail),
                      onTapReport: () => feedBloc.onTapReport(),
                    ),
                  ),
                );
              } else if (item is Product) {
                // Safety check for productDataModel
                if (productDataModel.allProducts.isEmpty) {
                  return const SizedBox();
                }

                final product = productDataModel.allProducts.firstWhere(
                  (element) =>
                      element.productReference != null &&
                      item.productReference != null &&
                      element.productReference == item.productReference,
                  orElse: () => Product(),
                );

                // Safety check for product
                if (product.productReference == null) {
                  return const SizedBox();
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: RepaintBoundary(
                    child: ProductDetailFullCard(
                      key: ValueKey('product_${product.productReference}'),
                      product: product,
                      isFromAddProduct: false,
                      isFullView: false,
                      isCustomTitleVisible: true,
                      customTitle: item.contentHeaderText,
                    ),
                  ),
                );
              }
              return const SizedBox();
            },
            childCount: feedBloc.feedList.length +
                (feedBloc.currentApiCallStatus != FeedState.Empty ? 1 : 0),
          ),
        );
      },
    );
  }

//endregion

  //region Pagination loading
  Widget paginationLoading() {
    return StreamBuilder<FeedPaginationState>(
        stream: feedBloc.feedPagination.feedPaginationStateCtrl.stream,
        initialData: FeedPaginationState.Loading,
        builder: (context, snapshot) {
          //If feed pagination is empty
          if (snapshot.data == FeedPaginationState.Empty) {
            return SliverToBoxAdapter(
                child: AllFeedScreen(
                    parentScrollController: feedBloc.scrollController));
          }
          return SliverToBoxAdapter(
              child: VisibilityDetector(
                  key: UniqueKey(),
                  onVisibilityChanged: (visibilityInfo) {
                    var visiblePercentage =
                        visibilityInfo.visibleFraction * 100;
                    if (visiblePercentage == 100) {
                      feedBloc.feedPagination.getPaginationFeeds();
                    }
                  },
                  child: AppCommonWidgets.appCircularProgress(
                      isPaginationProgress: true)));
        });
  }
  //endregion

//region All Feed Posts
  Widget allFeedPosts() {
    return AllFeedScreen(parentScrollController: feedBloc.scrollController);
  }
  //endregion
}
