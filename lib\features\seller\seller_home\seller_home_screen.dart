import 'package:badges/badges.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/banner/banner.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access.dart';
import 'package:swadesic/features/buyers/buyer_home/preview/preview.dart';
import 'package:swadesic/features/buyers/buyer_home/visited_store_grid/visited_store.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/feed/feed_screen.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/features/seller/community/community_screen.dart';
import 'package:swadesic/features/seller/seller_home/seller_home_screen_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import 'package:badges/badges.dart' as badges;
import 'package:swadesic/features/buyers/messaging/new_messaging_home_screen.dart';

class SellerHomeScreen extends StatefulWidget {
  const SellerHomeScreen({Key? key}) : super(key: key);

  @override
  State<SellerHomeScreen> createState() => _SellerHomeScreenState();
}

class _SellerHomeScreenState extends State<SellerHomeScreen>
    with SingleTickerProviderStateMixin {
  // region Bloc
  late SellerHomeScreenBloc sellerHomeScreenBloc;

  //Tab controller
  late TabController tabController =
      TabController(length: 2, vsync: this, initialIndex: 0);

// endregion

// region Init
  @override
  void initState() {
    //print("Init");
    sellerHomeScreenBloc = SellerHomeScreenBloc(context, tabController);
    sellerHomeScreenBloc.init();
    super.initState();
  }

//region Dispose
  @override
  void dispose() {
    imageCache.clear();
    sellerHomeScreenBloc.dispose();
    super.dispose();
  }

//endregion

// endregion

// region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body: SafeArea(child: body()),
    );
  }

// endregion

  //region App bar
  // AppBar appBar() {
  //   return AppCommonWidgets.mainAppBar(
  //     isCartVisible: false,
  //       isLeadingVisible: false,
  //       context: context,
  //       isCenterTitle: true,
  //       isCustomTitle: true,
  //       customTitleWidget:const Preview(),
  //       isDefaultMenuVisible: false);
  // }
  //endregion

  //region App bar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        isCartVisible: false,
        isLeadingVisible: false,
        context: context,
        isCenterTitle: true,
        isCustomTitle: true,
        customTitleWidget: const Preview(),
        customMenuButton: Row(
          children: [
            // Consumer<SellerOwnStoreInfoDataModel>(
            //   builder: (BuildContext context, SellerOwnStoreInfoDataModel value, Widget? child) {
            //     // //print("Store unread count is ${value.storeInfo!.unreadConversationsCount!}");

            //     //If user view
            //     if (AppConstants.appData.isUserView!) {
            //       return const SizedBox();
            //     }

            //     //If value is null then return
            //     if (value.storeInfo!.storeReference == null) {
            //       return Container(
            //         height: kToolbarHeight,
            //         margin: value.storeInfo!.unreadConversationsCount == 0 ? EdgeInsets.zero : EdgeInsets.only(right: 5, top: 5),
            //         child: GestureDetector(
            //           onTap: () async {
            //             //Else if static user then open login screen
            //             if (CommonMethods().isStaticUser()) {
            //               CommonMethods().goToSignUpFlow();
            //               return;
            //             }

            //             ///Go to message screen
            //             else {
            //               sellerHomeScreenBloc.goToMessagingScreen();
            //               //  CommonMethods.toastMessage(AppStrings.comingSoon, context);
            //             }
            //           },
            //           child: StreamBuilder<bool>(
            //               stream: AppConstants.bottomNavigationRefreshCtrl.stream,
            //               builder: (context, snapshot) {
            //                 return SvgPicture.asset(
            //                   AppImages.messageInAppBar,
            //                   height: kToolbarHeight - 30,
            //                   fit: BoxFit.contain,
            //                   color: AppColors.appBlack,
            //                 );
            //               }),
            //         ),
            //       );
            //     }

            //     return badges.Badge(
            //         showBadge: (value.storeInfo!.unreadConversationsCount! + value.storeInfo!.unreadConversationsCount!) == 0 ? false : true,
            //         badgeStyle: const badges.BadgeStyle(
            //           padding: EdgeInsets.all(5),
            //           badgeColor: AppColors.red,
            //         ),
            //         badgeContent: Text(
            //           // CommonMethods.calculateNotificationCount(number:notificationDataModel.getNotificationResponse!.notSeenCount!!),
            //           "${(value.storeInfo!.unreadConversationsCount!) == 0 ? "" : (value.storeInfo!.unreadConversationsCount!) > 9 ? "+9" : (value.storeInfo!.unreadConversationsCount!)}",
            //           style: const TextStyle(color: AppColors.appWhite, fontSize: 10, fontWeight: FontWeight.w700),
            //         ),
            //         //alignment: Alignment.center,
            //         position: BadgePosition.custom(top: 10, isCenter: false, end: 0),
            //         child: Container(
            //           height: kToolbarHeight,
            //           margin: value.storeInfo!.unreadConversationsCount == 0 ? EdgeInsets.zero : EdgeInsets.only(right: 5, top: 5),
            //           child: GestureDetector(
            //             onTap: () async {
            //               //Else if static user then open login screen
            //               if (CommonMethods().isStaticUser()) {
            //                 CommonMethods().goToSignUpFlow();
            //                 return;
            //               }

            //               ///Go to message screen
            //               else {
            //                 sellerHomeScreenBloc.goToMessagingScreen();
            //                 //  CommonMethods.toastMessage(AppStrings.comingSoon, context);
            //               }
            //             },
            //             child: StreamBuilder<bool>(
            //                 stream: AppConstants.bottomNavigationRefreshCtrl.stream,
            //                 builder: (context, snapshot) {
            //                   return SvgPicture.asset(
            //                     AppImages.messageInAppBar,
            //                     height: kToolbarHeight - 30,
            //                     fit: BoxFit.contain,
            //                     color: AppColors.appBlack,
            //                   );
            //                 }),
            //           ),
            //         ));
            //   },
            // ),

            Consumer<StoreDashboardDataModel>(
              builder: (BuildContext context, StoreDashboardDataModel value,
                  Widget? child) {
                // //print("Store unread count is ${value.storeInfo!.unreadConversationsCount!}");

                //If user view
                if (AppConstants.appData.isUserView!) {
                  return const SizedBox();
                }

                //If value is null then return
                if (value.storeDashBoard.storeReference == null) {
                  // return Container(
                  //   height: kToolbarHeight,
                  //   // margin: value.storeInfo!.unreadConversationsCount == 0 ? EdgeInsets.zero : EdgeInsets.only(right: 5, top: 5),
                  //   child: GestureDetector(
                  //     onTap: () async {
                  //       sellerHomeScreenBloc.goToSellerAllOrder();
                  //     },
                  //     child: StreamBuilder<bool>(
                  //         stream:
                  //             AppConstants.bottomNavigationRefreshCtrl.stream,
                  //         builder: (context, snapshot) {
                  //           return SvgPicture.asset(
                  //             AppImages.newOrdersIcon,
                  //             height: kToolbarHeight - 30,
                  //             fit: BoxFit.contain,
                  //           );
                  //         }),
                  //   ),
                  // );
                  return Container(
                    height: kToolbarHeight,
                    margin: EdgeInsets.only(right: 5, top: 8),
                    child: Row(
                      children: [
                        const SizedBox(width: 16),
                        Container(
                          height: kToolbarHeight,
                          margin: const EdgeInsets.only(right: 5, top: 5),
                          child: GestureDetector(
                            onTap: () async {
                              sellerHomeScreenBloc.goToSellerAllOrder();
                            },
                            child: SvgPicture.asset(
                              AppImages.newOrdersIcon,
                              height: kToolbarHeight - 30,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return badges.Badge(
                    showBadge: value.storeDashBoard.waitingForConfirmation == 0
                        ? false
                        : true,
                    badgeStyle: const badges.BadgeStyle(
                      padding: EdgeInsets.all(5),
                      badgeColor: AppColors.brandGreen,
                    ),
                    badgeContent: Text(
                      // CommonMethods.calculateNotificationCount(number:notificationDataModel.getNotificationResponse!.notSeenCount!!),
                      "${value.storeDashBoard.waitingForConfirmation == 0 ? "" : value.storeDashBoard.waitingForConfirmation! > 9 ? "+9" : value.storeDashBoard.waitingForConfirmation}",
                      style: const TextStyle(
                          color: AppColors.appWhite,
                          fontSize: 10,
                          fontWeight: FontWeight.w700),
                    ),
                    //alignment: Alignment.center,
                    position:
                        BadgePosition.custom(top: 10, isCenter: false, end: 0),
                    child: Container(
                      height: kToolbarHeight,
                      margin: EdgeInsets.only(right: 5, top: 6),
                      child: GestureDetector(
                        onTap: () async {
                          sellerHomeScreenBloc.goToSellerAllOrder();
                        },
                        child: StreamBuilder<bool>(
                            stream:
                                AppConstants.bottomNavigationRefreshCtrl.stream,
                            builder: (context, snapshot) {
                              return SvgPicture.asset(
                                AppImages.newOrdersIcon,
                                height: kToolbarHeight - 30,
                                fit: BoxFit.contain,
                              );
                            }),
                      ),
                    ));
              },
            ),

            Consumer<SellerOwnStoreInfoDataModel>(
              builder: (BuildContext context, SellerOwnStoreInfoDataModel value,
                  Widget? child) {
                // //print("Store unread count is ${value.storeInfo!.unreadConversationsCount!}");

                //If user view
                if (AppConstants.appData.isUserView!) {
                  return const SizedBox();
                }

                //If value is null then return
                if (value.storeInfo!.storeReference == null) {
                  return Container(
                    height: kToolbarHeight,
                    margin: value.storeInfo!.unreadConversationsCount == 0
                        ? EdgeInsets.zero
                        : EdgeInsets.only(right: 5, top: 5),
                    child: Row(
                      children: [
                        // Container(
                        //   height: kToolbarHeight,
                        //   margin: value.storeInfo!.unreadConversationsCount == 0 ? EdgeInsets.zero : EdgeInsets.only(right: 5, top: 5),
                        //   child: GestureDetector(
                        //     onTap: () async {
                        //       //Else if static user then open login screen
                        //       if (CommonMethods().isStaticUser()) {
                        //         CommonMethods().goToSignUpFlow();
                        //         return;
                        //       }

                        //       ///Go to message screen
                        //       else {
                        //         sellerHomeScreenBloc.goToMessagingScreen();
                        //         //  CommonMethods.toastMessage(AppStrings.comingSoon, context);
                        //       }
                        //     },
                        //     child: StreamBuilder<bool>(
                        //         stream: AppConstants.bottomNavigationRefreshCtrl.stream,
                        //         builder: (context, snapshot) {
                        //           return SvgPicture.asset(
                        //             AppImages.messageInAppBar,
                        //             height: kToolbarHeight - 30,
                        //             fit: BoxFit.contain,
                        //             color: AppColors.appBlack,
                        //           );
                        //         }),
                        //   ),
                        // ),
                        const SizedBox(width: 16),
                        Container(
                          height: kToolbarHeight,
                          margin: const EdgeInsets.only(right: 5, top: 5),
                          child: GestureDetector(
                            onTap: () {
                              if (CommonMethods().isStaticUser()) {
                                CommonMethods().goToSignUpFlow();
                                return;
                              }
                              // Use rootNavigator to push screen outside of PersistentTabView
                              Navigator.of(context, rootNavigator: true).push(
                                MaterialPageRoute(
                                    builder: (context) =>
                                        const NewMessagingHomeScreen()),
                              );
                            },
                            child: SvgPicture.asset(
                              AppImages.messageInAppBar,
                              height: kToolbarHeight - 30,
                              fit: BoxFit.contain,
                              color: AppColors.brandBlack,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return Row(
                  children: [
                    // badges.Badge(
                    //     showBadge: (value.storeInfo!.unreadConversationsCount! + value.storeInfo!.unreadConversationsCount!) == 0 ? false : true,
                    //     badgeStyle: const badges.BadgeStyle(
                    //       padding: EdgeInsets.all(5),
                    //       badgeColor: AppColors.red,
                    //     ),
                    //     badgeContent: Text(
                    //       // CommonMethods.calculateNotificationCount(number:notificationDataModel.getNotificationResponse!.notSeenCount!!),
                    //       "${(value.storeInfo!.unreadConversationsCount!) == 0 ? "" : (value.storeInfo!.unreadConversationsCount!) > 9 ? "+9" : (value.storeInfo!.unreadConversationsCount!)}",
                    //       style: const TextStyle(color: AppColors.appWhite, fontSize: 10, fontWeight: FontWeight.w700),
                    //     ),
                    //     //alignment: Alignment.center,
                    //     position: BadgePosition.custom(top: 10, isCenter: false, end: 0),
                    //     child: Container(
                    //       height: kToolbarHeight,
                    //       margin: value.storeInfo!.unreadConversationsCount == 0 ? EdgeInsets.zero : EdgeInsets.only(right: 5, top: 5),
                    //       child: GestureDetector(
                    //         onTap: () async {
                    //           //Else if static user then open login screen
                    //           if (CommonMethods().isStaticUser()) {
                    //             CommonMethods().goToSignUpFlow();
                    //             return;
                    //           }

                    //           ///Go to message screen
                    //           else {
                    //             sellerHomeScreenBloc.goToMessagingScreen();
                    //             //  CommonMethods.toastMessage(AppStrings.comingSoon, context);
                    //           }
                    //         },
                    //         child: StreamBuilder<bool>(
                    //             stream: AppConstants.bottomNavigationRefreshCtrl.stream,
                    //             builder: (context, snapshot) {
                    //               return SvgPicture.asset(
                    //                 AppImages.messageInAppBar,
                    //                 height: kToolbarHeight - 30,
                    //                 fit: BoxFit.contain,
                    //                 color: AppColors.appBlack,
                    //               );
                    //             }),
                    //       ),
                    //     )),
                    const SizedBox(width: 10),
                    Container(
                      height: kToolbarHeight,
                      margin: const EdgeInsets.only(right: 5, top: 5),
                      child: GestureDetector(
                        onTap: () {
                          if (CommonMethods().isStaticUser()) {
                            CommonMethods().goToSignUpFlow();
                            return;
                          }
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const NewMessagingHomeScreen()),
                          );
                        },
                        child: SvgPicture.asset(
                          AppImages.messageInAppBar,
                          height: kToolbarHeight - 30,
                          fit: BoxFit.contain,
                          color: AppColors.brandBlack,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),

            // GestureDetector(
            //   onTap: (){
            //     sellerHomeScreenBloc.goToSellerAllOrder();
            //   },
            //   child: SvgPicture.asset(
            //     AppImages.orders,
            //     height: kToolbarHeight - 20,
            //     fit: BoxFit.contain,
            //   ),
            // ),
            const SizedBox(width: 10),
            // GestureDetector(
            //   onTap: () async{
            //     //If web view
            //     if(kIsWeb){
            //       return  await CommonMethods.appDownloadDialog();
            //     }
            //     //Else if static user then open login screen
            //     else if(CommonMethods().isStaticUser()){
            //       return CommonMethods().goToSignUpFlow();
            //     }
            //     ///If on tap cart is not empty then go to shopping cart else call the onTapCart method
            //     else{
            //       var screen = const ShoppingCartScreen();
            //       var route = MaterialPageRoute(builder: (context) => screen);
            //       Navigator.push(context, route).then((value) {
            //         //Refresh cart
            //         AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
            //       });
            //     }
            //
            //   },
            //   child: StreamBuilder<bool>(
            //       stream: AppConstants.bottomNavigationRefreshCtrl.stream,
            //       builder: (context, snapshot) {
            //         return badges.Badge(
            //           showBadge: shoppingCartQuantityDataModel.productReferenceList.isNotEmpty,
            //           // padding: const EdgeInsets.all(5),
            //           badgeStyle: const badges.BadgeStyle(badgeColor: AppColors.brandGreen),
            //           badgeContent: Text(
            //             shoppingCartQuantityDataModel.productReferenceList.length > 9 ? "9+" : shoppingCartQuantityDataModel.productReferenceList.length.toString(),
            //             style: const TextStyle(color: AppColors.appWhite, fontSize: 10, fontWeight: FontWeight.w700),
            //           ),
            //           //alignment: Alignment.center,
            //           position: BadgePosition.topStart(start: 7),
            //           child: SvgPicture.asset(
            //             AppImages.basket,
            //             fit: BoxFit.contain,
            //             color: AppColors.appBlack,
            //           ),
            //         );
            //       }),
            // ),
            // const SizedBox(width: 10),
          ],
        ),
        isCustomMenuVisible: true,
        isDefaultMenuVisible: false);
  }

  //endregion

  //region Body
  Widget body() {
    return Column(
      children: [
        tabs(),
        divider(),
        Expanded(child: tabView()),
      ],
    );
  }

  //endregion

//region Tabs
  StreamBuilder<bool> tabs() {
    return StreamBuilder<bool>(
        stream: sellerHomeScreenBloc.tabRefreshCtrl.stream,
        builder: (context, snapshot) {
          return SizedBox(
            height: kToolbarHeight * 0.8,
            child: TabBar(
                controller: sellerHomeScreenBloc.tabController,
                indicator: const UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: AppColors.appBlack,
                    width: 2.0,
                  ),
                  // insets: const EdgeInsets.symmetric(horizontal: 16.0),
                ),
                onTap: (index) {
                  // notificationBloc.tabRefreshCtrl.sink.add(true);
                },
                padding: EdgeInsets.zero,
                // isScrollable: true,
                tabs: [
                  //Feed
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.feed,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  sellerHomeScreenBloc.tabController.index == 0
                                      ? AppColors.appBlack
                                      : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),

                  //Home
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.community,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  sellerHomeScreenBloc.tabController.index == 1
                                      ? AppColors.appBlack
                                      : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                ]),
          );
        });
  }

//endregion

//region Tab view
  TabBarView tabView() {
    return TabBarView(
        controller: sellerHomeScreenBloc.tabController,
        children: [
          //Feed screen
          FeedScreen(
              userOrStoreFeedReference: AppConstants.appData.storeReference!,
              externalScrollController: null, // Could be enhanced later
          ),
          // Community,
          CommunityScreen()
        ]);
  }

//endregion
}
